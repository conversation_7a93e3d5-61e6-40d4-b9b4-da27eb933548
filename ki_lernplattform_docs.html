<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KI-Lernplattform für Mathematik für Informatiker 1</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .timeline-item {
            position: relative;
            padding-left: 3rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            border: 4px solid white;
            box-shadow: 0 0 0 2px #667eea;
        }
        .timeline-item::after {
            content: '';
            position: absolute;
            left: 9px;
            top: 20px;
            width: 2px;
            height: calc(100% + 1rem);
            background: #e5e7eb;
        }
        .timeline-item:last-child::after {
            display: none;
        }
        .use-case-diagram {
            background: radial-gradient(circle at center, #f8fafc 0%, #e2e8f0 100%);
        }
        .process-step {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .tech-stack-item {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .risk-item {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .smooth-scroll {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 smooth-scroll">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <i class="fas fa-brain text-2xl text-indigo-600 mr-3"></i>
                    <h1 class="text-xl font-bold text-gray-900">KI-Lernplattform</h1>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#overview" class="text-gray-700 hover:text-indigo-600 transition-colors">Übersicht</a>
                    <a href="#challenges" class="text-gray-700 hover:text-indigo-600 transition-colors">Herausforderungen</a>
                    <a href="#use-cases" class="text-gray-700 hover:text-indigo-600 transition-colors">Use Cases</a>
                    <a href="#architecture" class="text-gray-700 hover:text-indigo-600 transition-colors">Architektur</a>
                    <a href="#roadmap" class="text-gray-700 hover:text-indigo-600 transition-colors">Roadmap</a>
                </div>
                <div class="md:hidden">
                    <button id="menu-toggle" class="text-gray-700 hover:text-indigo-600">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#overview" class="block px-3 py-2 text-gray-700 hover:text-indigo-600">Übersicht</a>
                <a href="#challenges" class="block px-3 py-2 text-gray-700 hover:text-indigo-600">Herausforderungen</a>
                <a href="#use-cases" class="block px-3 py-2 text-gray-700 hover:text-indigo-600">Use Cases</a>
                <a href="#architecture" class="block px-3 py-2 text-gray-700 hover:text-indigo-600">Architektur</a>
                <a href="#roadmap" class="block px-3 py-2 text-gray-700 hover:text-indigo-600">Roadmap</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                KI-Lernplattform für<br>
                <span class="text-yellow-300">Mathematik für Informatiker 1</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                Intelligente, personalisierte Lernunterstützung für Erstsemester-Studierende
            </p>
            <div class="flex justify-center space-x-4">
                <button class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    <i class="fas fa-play mr-2"></i>Demo starten
                </button>
                <button class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors">
                    <i class="fas fa-download mr-2"></i>Dokumentation
                </button>
            </div>
        </div>
    </section>

    <!-- Overview Section -->
    <section id="overview" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Projektübersicht</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Eine innovative KI-Lernplattform, die als intelligenter Tutor für Mathematik-Erstsemester fungiert
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center card-hover bg-gradient-to-br from-blue-50 to-indigo-100 p-8 rounded-xl">
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">200+ Studierende</h3>
                    <p class="text-gray-600">Skalierbare Unterstützung für große Lehrveranstaltungen</p>
                </div>
                
                <div class="text-center card-hover bg-gradient-to-br from-green-50 to-emerald-100 p-8 rounded-xl">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clock text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">24/7 Verfügbar</h3>
                    <p class="text-gray-600">Sofortige Hilfe ohne Wartezeiten</p>
                </div>
                
                <div class="text-center card-hover bg-gradient-to-br from-purple-50 to-violet-100 p-8 rounded-xl">
                    <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-language text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Mehrsprachig</h3>
                    <p class="text-gray-600">Unterstützung internationaler Studierender</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Challenges Section -->
    <section id="challenges" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Herausforderungen</h2>
                <p class="text-xl text-gray-600">Identifizierte Probleme im aktuellen Lernprozess</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-chart-line text-red-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold">Skalierbarkeit</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Individuelle Betreuung bei 200+ Studierenden unmöglich</p>
                    <div class="bg-red-50 p-3 rounded-lg">
                        <span class="text-red-600 font-medium">Durchschnittlich 2-3 Minuten pro Student</span>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-orange-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold">Verfügbarkeit</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Begrenzte Sprechstunden-Kapazität</p>
                    <div class="bg-orange-50 p-3 rounded-lg">
                        <span class="text-orange-600 font-medium">Wartezeiten von 1-2 Wochen</span>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-layer-group text-yellow-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold">Heterogenität</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Unterschiedliche mathematische Grundlagen</p>
                    <div class="bg-yellow-50 p-3 rounded-lg">
                        <span class="text-yellow-600 font-medium">Durchfallquote 40-50%</span>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-globe text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold">Sprachbarrieren</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Komplexe Terminologie auf Deutsch</p>
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <span class="text-blue-600 font-medium">Internationale Studierende benachteiligt</span>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-sync text-purple-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold">Feedback-Zyklen</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Verzögerte Rückmeldung zu Lösungen</p>
                    <div class="bg-purple-50 p-3 rounded-lg">
                        <span class="text-purple-600 font-medium">1-2 Wochen Bearbeitungszeit</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Use Cases Section -->
    <section id="use-cases" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Use Cases</h2>
                <p class="text-xl text-gray-600">8 priorisierte Anwendungsfälle der KI-Lernplattform</p>
                <div class="mt-6 bg-blue-50 p-4 rounded-lg max-w-4xl mx-auto">
                    <p class="text-sm text-blue-800">
                        <strong>Vision:</strong> Intelligenter, persönlicher KI-Tutor basierend auf offiziellen Kursmaterialien.
                        Kernprinzip: Hilfe zur Selbsthilfe, Förderung der Kollaboration und Abbau von Sprachbarrieren.
                    </p>
                </div>
            </div>
            
            <!-- Use Case Diagram -->
            <div class="use-case-diagram rounded-2xl p-8 mb-12 border-2 border-gray-200">
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">System-Übersicht</h3>
                </div>
                
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <!-- Left Side - Actors -->
                    <div class="space-y-6">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-user-graduate text-white text-2xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold">Student</h4>
                        </div>
                        
                        <div class="text-center">
                            <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-chalkboard-teacher text-white text-2xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold">Lehrende</h4>
                        </div>
                    </div>
                    
                    <!-- Right Side - Use Cases -->
                    <div class="space-y-4">
                        <div class="bg-white p-4 rounded-lg shadow-md border border-blue-200">
                            <h5 class="font-semibold text-blue-700">UC1: KI-Tutor für Übungsblätter</h5>
                            <p class="text-sm text-gray-600">Interaktive Unterstützung bei Aufgaben</p>
                        </div>
                        
                        <div class="bg-white p-4 rounded-lg shadow-md border border-green-200">
                            <h5 class="font-semibold text-green-700">UC2: Personalisierter Lernbegleiter</h5>
                            <p class="text-sm text-gray-600">Adaptive Lernunterstützung</p>
                        </div>
                        
                        <div class="bg-white p-4 rounded-lg shadow-md border border-purple-200">
                            <h5 class="font-semibold text-purple-700">UC3: Feedback-Cockpit</h5>
                            <p class="text-sm text-gray-600">Lehrenden-Dashboard</p>
                        </div>
                        
                        <div class="bg-white p-4 rounded-lg shadow-md border border-orange-200">
                            <h5 class="font-semibold text-orange-700">UC4: Foto-Analyse</h5>
                            <p class="text-sm text-gray-600">Handschrift-Erkennung</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Priority Overview -->
            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-8 mb-12">
                <h3 class="text-2xl font-bold text-center mb-6">Entwicklungspriorität der Use Cases</h3>
                <div class="grid md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span class="text-white font-bold text-xl">1-2</span>
                        </div>
                        <p class="text-sm font-medium">Kernfunktionalität</p>
                        <p class="text-xs text-gray-600">KI-Tutor & Foto-OCR</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span class="text-white font-bold text-xl">3-4</span>
                        </div>
                        <p class="text-sm font-medium">Personalisierung</p>
                        <p class="text-xs text-gray-600">Lernbegleiter & Gruppen</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span class="text-white font-bold text-xl">5-6</span>
                        </div>
                        <p class="text-sm font-medium">Erweiterung</p>
                        <p class="text-xs text-gray-600">Mehrsprachig & Aufgaben</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span class="text-white font-bold text-xl">7-8</span>
                        </div>
                        <p class="text-sm font-medium">Analytics</p>
                        <p class="text-xs text-gray-600">Cockpit & Kontext</p>
                    </div>
                </div>
            </div>

            <!-- Detailed Use Cases -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- UC1: KI-Tutor -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-robot text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">UC1: KI-Tutor für Übungsblätter</h3>
                            <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs">Priorität 1</span>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">Sokratische Fragestellung ohne Lösungspreisgabe</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Gestufte Hinweise bei Eigenleistung</li>
                        <li>• Verweis auf exakte Skript-Abschnitte</li>
                        <li>• Validierung von Lösungsansätzen</li>
                        <li>• Bei Ratlosigkeit: Skript-Verweis statt Tipp</li>
                    </ul>
                </div>

                <!-- UC4: Foto-Analyse -->
                <div class="bg-gradient-to-br from-purple-50 to-violet-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-camera text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">UC4: Foto-Analyse Handschrift</h3>
                            <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs">Priorität 2</span>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">OCR für handgeschriebene mathematische Formeln</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Automatische LaTeX-Konvertierung</li>
                        <li>• Bestätigung durch Studenten</li>
                        <li>• Analyse digitalisierter Lösungen</li>
                        <li>• Mobile-optimiert</li>
                    </ul>
                </div>

                <!-- UC2: Personalisierter Lernbegleiter -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-user-cog text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">UC2: Personalisierter Lernbegleiter</h3>
                            <span class="bg-orange-500 text-white px-2 py-1 rounded-full text-xs">Priorität 3</span>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">Adaptive Lernunterstützung mit Opt-in</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Erkennung wiederkehrender Schwierigkeiten</li>
                        <li>• Proaktives Feedback</li>
                        <li>• Explizite Zustimmung erforderlich</li>
                        <li>• Individuelle Kompetenzprofile</li>
                    </ul>
                </div>

                <!-- UC5: Lerngruppenbildung -->
                <div class="bg-gradient-to-br from-teal-50 to-cyan-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">UC5: Intelligente Lerngruppenbildung</h3>
                            <span class="bg-orange-500 text-white px-2 py-1 rounded-full text-xs">Priorität 4</span>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">Heterogene Lernpartner-Vorschläge</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Opt-in für Lernpartnerschaften</li>
                        <li>• Komplementäre Kompetenzprofile</li>
                        <li>• Anonymisierte Kontaktaufnahme</li>
                        <li>• Datenschutz-konforme Vermittlung</li>
                    </ul>
                </div>

                <!-- UC8: Mehrsprachigkeit -->
                <div class="bg-gradient-to-br from-pink-50 to-rose-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-pink-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-language text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">UC8: Mehrsprachige Wissensvermittlung</h3>
                            <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs">Priorität 5</span>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">Fachterminologisch korrekte Übersetzungen</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Englisch, Spanisch, Türkisch etc.</li>
                        <li>• Idiomatisch korrekte Erklärungen</li>
                        <li>• Validiertes mehrsprachiges Glossar</li>
                        <li>• Kontextsensitive Übersetzung</li>
                    </ul>
                </div>

                <!-- UC6: Aufgabengenerierung -->
                <div class="bg-gradient-to-br from-amber-50 to-yellow-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-amber-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-tasks text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">UC6: Generierung von Übungsaufgaben</h3>
                            <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs">Priorität 6</span>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">Zusätzliche Übungen für gezieltes Training</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Themenspezifische Aufgaben</li>
                        <li>• Mathematisch korrekt & eindeutig</li>
                        <li>• Lösungen zur Selbstkontrolle</li>
                        <li>• Didaktisch sinnvolle Progression</li>
                    </ul>
                </div>

                <!-- UC3: Feedback-Cockpit -->
                <div class="bg-gradient-to-br from-orange-50 to-amber-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-chart-bar text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">UC3: Feedback-Cockpit für Lehrende</h3>
                            <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">Priorität 7</span>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">Anonymisierte Kurs-Analyse</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Häufigste Problembereiche</li>
                        <li>• Vollständig anonymisierte Daten</li>
                        <li>• Aggregierte Lernfortschritt-Statistiken</li>
                        <li>• Gezielte Vorlesungsanpassungen</li>
                    </ul>
                </div>

                <!-- UC7: Kontextualisierung -->
                <div class="bg-gradient-to-br from-indigo-50 to-blue-100 p-6 rounded-xl card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-lightbulb text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">UC7: Kontextualisierung & Anwendungsbezug</h3>
                            <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">Priorität 8</span>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">Motivation durch Praxisbezug</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Relevanz für Informatik-Studium</li>
                        <li>• Berufsleben-Bezug</li>
                        <li>• Konkrete Anwendungsbeispiele</li>
                        <li>• Kuratierte, validierte Beispiele</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- BPMN Process Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Detaillierter BPMN-Prozess</h2>
                <p class="text-xl text-gray-600">Vollständiger Lernprozess mit KI-Unterstützung</p>
            </div>

            <!-- BPMN Diagram Image -->
            <div class="bg-white rounded-2xl p-8 shadow-lg mb-8">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">BPMN-Diagramm: Studentischer Lernprozess</h3>
                    <p class="text-gray-600 mt-2">Von der Vorlesung bis zur Problemlösung mit KI-Tutor</p>
                </div>

                <!-- Simplified BPMN representation -->
                <div class="bg-gray-50 rounded-xl p-6 overflow-x-auto">
                    <div class="flex items-center space-x-4 min-w-max">
                        <!-- Start -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white"></i>
                            </div>
                            <p class="text-xs mt-2 text-center">Start</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- Vorlesung besuchen -->
                        <div class="flex flex-col items-center">
                            <div class="bg-blue-100 border-2 border-blue-400 rounded-lg p-3 min-w-24">
                                <i class="fas fa-chalkboard-teacher text-blue-600 text-lg"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Vorlesung besuchen</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- Dozent erklärt -->
                        <div class="flex flex-col items-center">
                            <div class="bg-blue-100 border-2 border-blue-400 rounded-lg p-3 min-w-24">
                                <i class="fas fa-user-tie text-blue-600 text-lg"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Dozent erklärt den Inhalt</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- Student macht sich Notizen -->
                        <div class="flex flex-col items-center">
                            <div class="bg-blue-100 border-2 border-blue-400 rounded-lg p-3 min-w-24">
                                <i class="fas fa-edit text-blue-600 text-lg"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Student macht sich Notizen</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- Decision: Frage möglich? -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-yellow-400 transform rotate-45 flex items-center justify-center">
                                <i class="fas fa-question text-white transform -rotate-45"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Frage möglich?</p>
                        </div>
                    </div>

                    <!-- Second row -->
                    <div class="mt-8 flex items-center space-x-4 min-w-max">
                        <!-- Ja path -->
                        <div class="flex flex-col items-center">
                            <div class="bg-green-100 border-2 border-green-400 rounded-lg p-3 min-w-24">
                                <i class="fas fa-comments text-green-600 text-lg"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Frage wird gestellt</p>
                            <span class="text-xs text-green-600 font-medium">Ja</span>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- Nein path -->
                        <div class="flex flex-col items-center">
                            <div class="bg-orange-100 border-2 border-orange-400 rounded-lg p-3 min-w-24">
                                <i class="fas fa-file-alt text-orange-600 text-lg"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Unklare Punkte bleiben offen</p>
                            <span class="text-xs text-orange-600 font-medium">Nein</span>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- Student muss Zuhause lernen -->
                        <div class="flex flex-col items-center">
                            <div class="bg-blue-100 border-2 border-blue-400 rounded-lg p-3 min-w-24">
                                <i class="fas fa-home text-blue-600 text-lg"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Student muss Zuhause lernen</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- Verständnisse Probleme treffen -->
                        <div class="flex flex-col items-center">
                            <div class="bg-red-100 border-2 border-red-400 rounded-lg p-3 min-w-24">
                                <i class="fas fa-exclamation-triangle text-red-600 text-lg"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Verständnisse Probleme treffen</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- Nach Hilfe suchen -->
                        <div class="flex flex-col items-center">
                            <div class="bg-purple-100 border-2 border-purple-400 rounded-lg p-3 min-w-24">
                                <i class="fas fa-search text-purple-600 text-lg"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Nach Hilfe im Internet oder Freunde suchen</p>
                        </div>
                    </div>

                    <!-- Third row -->
                    <div class="mt-8 flex items-center space-x-4 min-w-max">
                        <!-- Decision: Keine Vorschläge -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-yellow-400 transform rotate-45 flex items-center justify-center">
                                <i class="fas fa-times text-white transform -rotate-45"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Keine Vorschläge</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- Unvollständige Prüfungs-Vorbereitung -->
                        <div class="flex flex-col items-center">
                            <div class="bg-red-100 border-2 border-red-400 rounded-lg p-3 min-w-24">
                                <i class="fas fa-graduation-cap text-red-600 text-lg"></i>
                            </div>
                            <p class="text-xs mt-2 text-center max-w-20">Unvollständige Prüfungs-Vorbereitung</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-400 min-w-8"></div>

                        <!-- End -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center border-4 border-red-700">
                                <i class="fas fa-stop text-white"></i>
                            </div>
                            <p class="text-xs mt-2 text-center">Problem bleibt</p>
                        </div>
                    </div>
                </div>

                <div class="mt-8 bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-lightbulb mr-2"></i>
                        KI-Lösung: Intelligenter Tutor
                    </h4>
                    <p class="text-blue-700 text-sm">
                        Die KI-Lernplattform durchbricht diesen Kreislauf durch sofortige, verfügbare Hilfe mit sokratischer Methode,
                        die zur Selbstständigkeit führt statt Abhängigkeit zu schaffen.
                    </p>
                </div>
            </div>

            <!-- Simplified Process Flow -->
            <div class="bg-white rounded-2xl p-8 shadow-lg">
                <h3 class="text-xl font-bold text-center mb-6">KI-Tutor Prozessablauf (Vereinfacht)</h3>
                <div class="overflow-x-auto">
                    <div class="flex items-center space-x-4 min-w-max">
                        <div class="process-step text-white p-4 rounded-lg text-center min-w-32">
                            <i class="fas fa-play-circle text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Start</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-300"></div>

                        <div class="process-step text-white p-4 rounded-lg text-center min-w-32">
                            <i class="fas fa-edit text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Aufgabe eingeben</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-300"></div>

                        <div class="process-step text-white p-4 rounded-lg text-center min-w-32">
                            <i class="fas fa-search text-2xl mb-2"></i>
                            <p class="text-sm font-medium">OCR-Analyse</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-300"></div>

                        <div class="process-step text-white p-4 rounded-lg text-center min-w-32">
                            <i class="fas fa-brain text-2xl mb-2"></i>
                            <p class="text-sm font-medium">KI-Analyse</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-300"></div>

                        <div class="process-step text-white p-4 rounded-lg text-center min-w-32">
                            <i class="fas fa-comments text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Sokratisches Feedback</p>
                        </div>

                        <div class="flex-1 h-0.5 bg-gray-300"></div>

                        <div class="process-step text-white p-4 rounded-lg text-center min-w-32">
                            <i class="fas fa-check-circle text-2xl mb-2"></i>
                            <p class="text-sm font-medium">Lernvalidierung</p>
                        </div>
                    </div>
                </div>

                <div class="mt-8 grid md:grid-cols-3 gap-6">
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-700 mb-2">Ausnahme: Unvollständige Eingabe</h4>
                        <p class="text-sm text-red-600">Nachfrage nach fehlenden Informationen oder Eigenleistung</p>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-yellow-700 mb-2">Ausnahme: Komplette Ratlosigkeit</h4>
                        <p class="text-sm text-yellow-600">Verweis auf exakte Skript-Abschnitte statt direkter Tipps</p>
                    </div>

                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-700 mb-2">Ausnahme: Falsche Richtung</h4>
                        <p class="text-sm text-blue-600">Sanfte Korrektur ohne Lösungspreisgabe durch sokratische Fragen</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Architecture Section -->
    <section id="architecture" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Technische Architektur</h2>
                <p class="text-xl text-gray-600">Moderne, skalierbare Systemarchitektur</p>
            </div>
            
            <!-- Architecture Diagram -->
            <div class="bg-gray-50 rounded-2xl p-8 mb-12">
                <div class="grid md:grid-cols-3 gap-8">
                    <!-- Frontend -->
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-gray-800 mb-6">Frontend</h3>
                        <div class="space-y-4">
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fab fa-react text-2xl mb-2"></i>
                                <p class="font-medium">React.js</p>
                                <p class="text-sm opacity-90">TypeScript</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fas fa-mobile-alt text-2xl mb-2"></i>
                                <p class="font-medium">Tailwind CSS</p>
                                <p class="text-sm opacity-90">Responsive Design</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fas fa-calculator text-2xl mb-2"></i>
                                <p class="font-medium">MathJax</p>
                                <p class="text-sm opacity-90">LaTeX-Rendering</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Backend -->
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-gray-800 mb-6">Backend & KI</h3>
                        <div class="space-y-4">
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fas fa-robot text-2xl mb-2"></i>
                                <p class="font-medium">LangChain</p>
                                <p class="text-sm opacity-90">KI-Orchestrierung</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fas fa-brain text-2xl mb-2"></i>
                                <p class="font-medium">GPT-4 / Llama 3</p>
                                <p class="text-sm opacity-90">Large Language Model</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fas fa-camera text-2xl mb-2"></i>
                                <p class="font-medium">Mathpix / Nougat</p>
                                <p class="text-sm opacity-90">Mathe-OCR</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fas fa-search text-2xl mb-2"></i>
                                <p class="font-medium">Pinecone / Weaviate</p>
                                <p class="text-sm opacity-90">Vektordatenbank (RAG)</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fab fa-python text-2xl mb-2"></i>
                                <p class="font-medium">FastAPI</p>
                                <p class="text-sm opacity-90">Python Backend</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fas fa-database text-2xl mb-2"></i>
                                <p class="font-medium">PostgreSQL</p>
                                <p class="text-sm opacity-90">Relationale DB</p>
                            </div>
                        </div>
                    </div>

                    <!-- Infrastructure -->
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-gray-800 mb-6">Infrastructure</h3>
                        <div class="space-y-4">
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fab fa-docker text-2xl mb-2"></i>
                                <p class="font-medium">Docker</p>
                                <p class="text-sm opacity-90">Containerization</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fab fa-aws text-2xl mb-2"></i>
                                <p class="font-medium">AWS</p>
                                <p class="text-sm opacity-90">Cloud Platform</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fas fa-shield-alt text-2xl mb-2"></i>
                                <p class="font-medium">OAuth 2.0</p>
                                <p class="text-sm opacity-90">Authentifizierung</p>
                            </div>
                            <div class="tech-stack-item text-white p-4 rounded-lg">
                                <i class="fas fa-globe text-2xl mb-2"></i>
                                <p class="font-medium">Tavily AI</p>
                                <p class="text-sm opacity-90">Web-Suche Fallback</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Architecture -->
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-8 mb-12">
                <h3 class="text-2xl font-bold text-center mb-8">Datenarchitektur & Verarbeitung</h3>
                <div class="grid md:grid-cols-3 gap-8">
                    <!-- Kursinhalte -->
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-book text-white text-xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold">Kursinhalte</h4>
                        </div>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>• Vorlesungsskripte (PDF, LaTeX)</li>
                            <li>• Wöchentliche Übungsaufgaben</li>
                            <li>• Detaillierte Musterlösungen</li>
                            <li>• Kuratierte Anwendungsbeispiele</li>
                            <li>• Mehrsprachiges Fachglossar</li>
                        </ul>
                        <div class="mt-4 bg-blue-50 p-3 rounded-lg">
                            <p class="text-xs text-blue-700 font-medium">
                                Zweck: RAG-Wissensbasis ("Single Source of Truth")
                            </p>
                        </div>
                    </div>

                    <!-- Nutzerdaten -->
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-user text-white text-xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold">Nutzerdaten</h4>
                        </div>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>• Stammdaten (Name, E-Mail, Matrikelnummer)</li>
                            <li>• Explizite Opt-in-Zustimmungen</li>
                            <li>• Semester & Kurs-Zuordnung</li>
                            <li>• Sprachpräferenzen</li>
                        </ul>
                        <div class="mt-4 bg-green-50 p-3 rounded-lg">
                            <p class="text-xs text-green-700 font-medium">
                                Zweck: Authentifizierung & DSGVO-Konformität
                            </p>
                        </div>
                    </div>

                    <!-- Interaktionsdaten -->
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-chart-line text-white text-xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold">Interaktionsdaten</h4>
                        </div>
                        <ul class="text-sm text-gray-600 space-y-2">
                            <li>• Gestellte Fragen & Lösungsversuche</li>
                            <li>• Genutzte Hilfestellungen pro Aufgabe</li>
                            <li>• Zeitstempel der Interaktionen</li>
                            <li>• Feedback zu KI-Antworten</li>
                        </ul>
                        <div class="mt-4 bg-purple-50 p-3 rounded-lg">
                            <p class="text-xs text-purple-700 font-medium">
                                Zweck: Personalisierung (pseudonymisiert) & Analytics (anonymisiert)
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Details -->
            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl">
                    <h3 class="text-xl font-semibold mb-4 flex items-center">
                        <i class="fas fa-cogs text-blue-600 mr-3"></i>
                        API-Design
                    </h3>
                    <ul class="space-y-2 text-gray-700">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            RESTful API mit OpenAPI-Spezifikation
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            Asynchrone Verarbeitung für KI-Anfragen
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            Rate Limiting und Caching
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            Websocket für Echtzeit-Chat
                        </li>
                    </ul>
                </div>

                <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-6 rounded-xl">
                    <h3 class="text-xl font-semibold mb-4 flex items-center">
                        <i class="fas fa-shield-alt text-green-600 mr-3"></i>
                        Sicherheit & Datenschutz
                    </h3>
                    <ul class="space-y-2 text-gray-700">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            DSGVO-konforme Datenverarbeitung
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            End-to-End Verschlüsselung
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            Anonymisierung von Lerndaten
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            Sichere Authentifizierung
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Central Challenges Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Zentrale Herausforderungen</h2>
                <p class="text-xl text-gray-600">Kritische Aspekte und Lösungsansätze für die KI-Lernplattform</p>
            </div>

            <div class="grid md:grid-cols-2 gap-8">
                <!-- Pädagogische Qualität -->
                <div class="bg-gradient-to-br from-red-50 to-pink-100 p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">Pädagogische Qualität & Anti-Cheating</h3>
                    </div>
                    <div class="mb-4">
                        <h4 class="font-semibold text-red-700 mb-2">Herausforderung:</h4>
                        <p class="text-gray-700 text-sm">Vermeidung von Lösungspreisgabe bei gleichzeitiger effektiver Hilfestellung</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-700 mb-2">Lösungsansatz:</h4>
                        <ul class="text-sm text-red-600 space-y-1">
                            <li>• Gestuftes Freigabemodell mit Eigenleistung</li>
                            <li>• Sokratische Fragestellung statt direkter Antworten</li>
                            <li>• Bei Ratlosigkeit: Skript-Verweis statt Tipps</li>
                            <li>• Validierung von Lösungsansätzen</li>
                        </ul>
                    </div>
                </div>

                <!-- Mathematische Notation -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-calculator text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">Mathematische Notation</h3>
                    </div>
                    <div class="mb-4">
                        <h4 class="font-semibold text-blue-700 mb-2">Herausforderung:</h4>
                        <p class="text-gray-700 text-sm">Interpretation und Verarbeitung komplexer mathematischer Formeln</p>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-700 mb-2">Lösungsansatz:</h4>
                        <ul class="text-sm text-blue-600 space-y-1">
                            <li>• Spezialisierte OCR-Modelle (Mathpix/Nougat)</li>
                            <li>• LaTeX-Training für semantische Verarbeitung</li>
                            <li>• Handschrift-Erkennung mit Bestätigung</li>
                            <li>• Robuste Formel-Parsing-Pipeline</li>
                        </ul>
                    </div>
                </div>

                <!-- Datenschutz & DSGVO -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">Datenschutz & DSGVO-Konformität</h3>
                    </div>
                    <div class="mb-4">
                        <h4 class="font-semibold text-green-700 mb-2">Herausforderung:</h4>
                        <p class="text-gray-700 text-sm">Höchste Priorität für Datenschutz bei personalisierten KI-Diensten</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-700 mb-2">Lösungsansatz:</h4>
                        <ul class="text-sm text-green-600 space-y-1">
                            <li>• Striktes Opt-in-Prinzip für alle personalisierten Funktionen</li>
                            <li>• Vollständige Transparenz über Datenverwendung</li>
                            <li>• Anonymisierung für Lehrenden-Analytics</li>
                            <li>• Anonyme Kontaktaufnahme bei Lerngruppenbildung</li>
                        </ul>
                    </div>
                </div>

                <!-- Qualität generierter Inhalte -->
                <div class="bg-gradient-to-br from-purple-50 to-violet-100 p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-tasks text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">Qualität generierter Inhalte</h3>
                    </div>
                    <div class="mb-4">
                        <h4 class="font-semibold text-purple-700 mb-2">Herausforderung:</h4>
                        <p class="text-gray-700 text-sm">Mathematisch korrekte, eindeutig lösbare und didaktisch sinnvolle KI-generierte Aufgaben</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-700 mb-2">Lösungsansatz:</h4>
                        <ul class="text-sm text-purple-600 space-y-1">
                            <li>• Robustes, gut trainiertes Modell</li>
                            <li>• Validierungsmechanismus durch Lehrende</li>
                            <li>• Qualitätskontrolle-Pipeline</li>
                            <li>• Feedback-Loop für kontinuierliche Verbesserung</li>
                        </ul>
                    </div>
                </div>

                <!-- Fachterminologische Übersetzung -->
                <div class="bg-gradient-to-br from-orange-50 to-amber-100 p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-language text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">Fachterminologische Übersetzung</h3>
                    </div>
                    <div class="mb-4">
                        <h4 class="font-semibold text-orange-700 mb-2">Herausforderung:</h4>
                        <p class="text-gray-700 text-sm">Korrekte, kontextsensitive Übersetzung mathematischer Fachbegriffe</p>
                    </div>
                    <div class="bg-orange-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-orange-700 mb-2">Lösungsansatz:</h4>
                        <ul class="text-sm text-orange-600 space-y-1">
                            <li>• Spezialisiertes Übersetzungsmodell</li>
                            <li>• Validiertes mehrsprachiges Glossar</li>
                            <li>• Kontextsensitive Begriffserkennung</li>
                            <li>• Beispiel: "Körper" → "field" (nicht "body")</li>
                        </ul>
                    </div>
                </div>

                <!-- Anwendungsbeispiele -->
                <div class="bg-gradient-to-br from-teal-50 to-cyan-100 p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-lightbulb text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold">Relevanz & Korrektheit der Anwendungsbeispiele</h3>
                    </div>
                    <div class="mb-4">
                        <h4 class="font-semibold text-teal-700 mb-2">Herausforderung:</h4>
                        <p class="text-gray-700 text-sm">Fachlich korrekte, aktuelle und verständliche Praxisbeispiele für Erstsemester</p>
                    </div>
                    <div class="bg-teal-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-teal-700 mb-2">Lösungsansatz:</h4>
                        <ul class="text-sm text-teal-600 space-y-1">
                            <li>• Kuratierte, von Lehrenden validierte Beispiele</li>
                            <li>• Praxisnahe Anwendungsfälle aus der Informatik</li>
                            <li>• Regelmäßige Aktualisierung der Wissensbasis</li>
                            <li>• Erstsemester-gerechte Komplexität</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Risk Analysis Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Risikoanalyse</h2>
                <p class="text-xl text-gray-600">Identifizierte Risiken und Gegenmaßnahmen</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 risk-item rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">KI-Halluzinationen</h3>
                            <span class="text-red-600 text-sm font-medium">Hoch</span>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Falsche oder irreführende Antworten der KI</p>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <p class="text-sm font-medium text-gray-700">Gegenmaßnahmen:</p>
                        <ul class="text-sm text-gray-600 mt-1">
                            <li>• Prompt Engineering</li>
                            <li>• Fact-Checking Pipeline</li>
                            <li>• Human-in-the-Loop</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 risk-item rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-user-shield text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">Datenschutz</h3>
                            <span class="text-orange-600 text-sm font-medium">Mittel</span>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Sensible Studierendendaten</p>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <p class="text-sm font-medium text-gray-700">Gegenmaßnahmen:</p>
                        <ul class="text-sm text-gray-600 mt-1">
                            <li>• DSGVO-Compliance</li>
                            <li>• Datenminimierung</li>
                            <li>• Anonymisierung</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 risk-item rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">Lernabhängigkeit</h3>
                            <span class="text-yellow-600 text-sm font-medium">Mittel</span>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Übermäßige Abhängigkeit von KI-Hilfe</p>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <p class="text-sm font-medium text-gray-700">Gegenmaßnahmen:</p>
                        <ul class="text-sm text-gray-600 mt-1">
                            <li>• Sokratische Methode</li>
                            <li>• Schrittweise Hilfe</li>
                            <li>• Selbstständigkeits-Förderung</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 risk-item rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-server text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">Skalierbarkeit</h3>
                            <span class="text-orange-600 text-sm font-medium">Mittel</span>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Performance bei hoher Nutzerzahl</p>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <p class="text-sm font-medium text-gray-700">Gegenmaßnahmen:</p>
                        <ul class="text-sm text-gray-600 mt-1">
                            <li>• Auto-Scaling</li>
                            <li>• Caching-Strategien</li>
                            <li>• Load Balancing</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 risk-item rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-dollar-sign text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">API-Kosten</h3>
                            <span class="text-yellow-600 text-sm font-medium">Niedrig</span>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Unvorhersehbare OpenAI-Kosten</p>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <p class="text-sm font-medium text-gray-700">Gegenmaßnahmen:</p>
                        <ul class="text-sm text-gray-600 mt-1">
                            <li>• Budget-Limits</li>
                            <li>• Kostenmonitoring</li>
                            <li>• Effiziente Prompts</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 risk-item rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">Nutzerakzeptanz</h3>
                            <span class="text-green-600 text-sm font-medium">Niedrig</span>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Widerstand gegen KI-Unterstützung</p>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <p class="text-sm font-medium text-gray-700">Gegenmaßnahmen:</p>
                        <ul class="text-sm text-gray-600 mt-1">
                            <li>• Transparenz</li>
                            <li>• Schulungen</li>
                            <li>• Schrittweise Einführung</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Roadmap Section -->
    <section id="roadmap" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Entwicklungs-Roadmap</h2>
                <p class="text-xl text-gray-600">Phasenweise Umsetzung der KI-Lernplattform</p>
            </div>

            <div class="space-y-8">
                <!-- Phase 1 -->
                <div class="timeline-item">
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-900">Phase 1: MVP</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Grundlegende KI-Tutor Funktionalität</p>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">Kernfeatures:</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• Text-basierter KI-Chat</li>
                                    <li>• Grundlegende OCR-Funktionalität</li>
                                    <li>• Einfache Benutzeroberfläche</li>
                                    <li>• Basis-Authentifizierung</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">Technische Ziele:</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• FastAPI Backend</li>
                                    <li>• React Frontend</li>
                                    <li>• OpenAI Integration</li>
                                    <li>• PostgreSQL Setup</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Phase 2 -->
                <div class="timeline-item">
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-900">Phase 2: Erweiterte Features</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Personalisierung und erweiterte OCR</p>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">Neue Features:</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• Erweiterte Foto-Analyse</li>
                                    <li>• Lernfortschritt-Tracking</li>
                                    <li>• Personalisierte Empfehlungen</li>
                                    <li>• Mobile App (PWA)</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">Verbesserungen:</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• Bessere OCR-Genauigkeit</li>
                                    <li>• Performance-Optimierung</li>
                                    <li>• UI/UX Verbesserungen</li>
                                    <li>• Mehrsprachigkeit</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Phase 3 -->
                <div class="timeline-item">
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-900">Phase 3: Analytics & Skalierung</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Lehrenden-Dashboard und Skalierung</p>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">Analytics:</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• Feedback-Cockpit für Lehrende</li>
                                    <li>• Lernanalytics Dashboard</li>
                                    <li>• Automatische Reports</li>
                                    <li>• A/B Testing Framework</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">Skalierung:</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• Auto-Scaling Infrastructure</li>
                                    <li>• CDN Integration</li>
                                    <li>• Monitoring & Alerting</li>
                                    <li>• Load Testing</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Phase 4 -->
                <div class="timeline-item">
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-bold text-gray-900">Phase 4: KI-Optimierung</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Erweiterte KI-Funktionen und Optimierung</p>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">KI-Verbesserungen:</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• Fine-tuning für Mathematik</li>
                                    <li>• Multimodal AI (Text + Bild)</li>
                                    <li>• Adaptive Lernpfade</li>
                                    <li>• Predictive Analytics</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">Integration:</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• LMS-Integration (Moodle)</li>
                                    <li>• API für Drittanbieter</li>
                                    <li>• Gamification Elements</li>
                                    <li>• Voice Interface</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Close mobile menu if open
                    const mobileMenu = document.getElementById('mobile-menu');
                    mobileMenu.classList.add('hidden');
                }
            });
        });

        // Add scroll effect to navigation
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.classList.add('shadow-xl');
            } else {
                nav.classList.remove('shadow-xl');
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all cards for animation
        document.querySelectorAll('.card-hover').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Demo button functionality
        const demoButtons = document.querySelectorAll('button');
        demoButtons.forEach(button => {
            if (button.textContent.includes('Demo starten')) {
                button.addEventListener('click', function() {
                    alert('Demo wird in Kürze verfügbar sein!');
                });
            }
        });
    </script>
</body>
</html>